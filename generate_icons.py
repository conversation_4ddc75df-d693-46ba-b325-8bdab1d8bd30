#!/usr/bin/env python3
import os
from PIL import Image, ImageDraw

def create_icon(size, path):
    # Create a simple blue circle icon
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw a blue circle with white game controller symbol
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill='#2196F3')
    
    # Simple game controller representation (rectangle with smaller rectangles)
    center_x, center_y = size // 2, size // 2
    controller_width = size // 3
    controller_height = size // 6
    
    # Main controller body
    draw.rectangle([
        center_x - controller_width//2, 
        center_y - controller_height//2,
        center_x + controller_width//2, 
        center_y + controller_height//2
    ], fill='white')
    
    # Left and right grips
    grip_size = size // 12
    draw.rectangle([
        center_x - controller_width//2 - grip_size//2, 
        center_y - grip_size//2,
        center_x - controller_width//2 + grip_size//2, 
        center_y + grip_size//2
    ], fill='white')
    
    draw.rectangle([
        center_x + controller_width//2 - grip_size//2, 
        center_y - grip_size//2,
        center_x + controller_width//2 + grip_size//2, 
        center_y + grip_size//2
    ], fill='white')
    
    # Save the image
    os.makedirs(os.path.dirname(path), exist_ok=True)
    img.save(path, 'PNG')

# Icon sizes for different densities
icon_sizes = {
    'mipmap-mdpi': 48,
    'mipmap-hdpi': 72,
    'mipmap-xhdpi': 96,
    'mipmap-xxhdpi': 144,
    'mipmap-xxxhdpi': 192
}

# Create icons
for folder, size in icon_sizes.items():
    create_icon(size, f'app/src/main/res/{folder}/ic_launcher.png')
    create_icon(size, f'app/src/main/res/{folder}/ic_launcher_round.png')

print("Icons generated successfully!")
