"""
Turn Me On - Android Version (FIXED)
A mobile game where you click lightbulbs to keep the lights on while avoiding gremlins.
Converted from pygame to Kivy for Android compatibility with proper file handling.
"""

from kivy.app import App
from kivy.uix.widget import Widget
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.floatlayout import FloatLayout
from kivy.graphics import Color, Ellipse, Rectangle, Line
from kivy.graphics.texture import Texture
from kivy.core.image import Image as CoreImage
from kivy.clock import Clock
from kivy.core.audio import SoundLoader
from kivy.core.window import Window
from kivy.vector import Vector
from kivy.animation import Animation
from kivy.properties import NumericProperty, BooleanProperty, StringProperty, ListProperty
from kivy.resources import resource_find
from kivy.storage.jsonstore import JsonStore

import random
import math
import json
import os

# Import UI components
from ui import GameUI, GameOverPopup, DifficultySelectionPopup

# --- Constants ---
SCREEN_WIDTH = 480
SCREEN_HEIGHT = 800
FPS = 60

# Colors (RGBA format for Kivy)
WHITE = (1, 1, 1, 1)
BLACK = (0, 0, 0, 1)
RED = (1, 0, 0, 1)
GREEN = (0, 1, 0, 1)
BLUE = (0, 0, 1, 1)
YELLOW = (1, 1, 0, 1)
PURPLE = (0.5, 0, 0.5, 1)
ORANGE = (1, 0.65, 0, 1)
CYAN = (0, 1, 1, 1)
PINK = (1, 0.75, 0.8, 1)

# Game Settings
BRIGHTNESS_DECAY = 0.15  # Brightness points per second
BULB_ON_VALUE = 25       # Brightness points gained when a bulb is turned on
BULB_OFF_VALUE = -10     # Brightness points lost when a bulb is turned off
GREMLIN_DAMAGE = -15     # Brightness points lost when hit by gremlin
STAR_BONUS = 10          # Brightness points gained when collecting a star

def get_asset_path(filename):
    """Get the correct path for assets on Android"""
    try:
        # Try to find the resource in the app bundle
        path = resource_find(filename)
        if path:
            return path
        # Fallback to assets directory
        return os.path.join('assets', filename)
    except:
        # Final fallback
        return filename

class Bulb(Widget):
    """Represents a lightbulb that can be clicked to turn on/off"""
    
    brightness = NumericProperty(0)
    is_on = BooleanProperty(False)
    is_broken = BooleanProperty(False)
    
    def __init__(self, x, y, **kwargs):
        super().__init__(**kwargs)
        self.pos = (x, y)
        self.size = (60, 60)
        self.brightness = random.randint(50, 100)
        self.is_on = False
        self.is_broken = False
        self.max_brightness = 100
        
        # Load textures with proper Android paths
        try:
            bulb_off_path = get_asset_path('bulb_off.png')
            bulb_on_path = get_asset_path('bulb_on.png')
            self.texture_off = CoreImage(bulb_off_path).texture
            self.texture_on = CoreImage(bulb_on_path).texture
        except Exception as e:
            print(f"Error loading bulb textures: {e}")
            # Create fallback textures
            self.texture_off = None
            self.texture_on = None
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.update_graphics()
    
    def update_graphics(self, *args):
        """Update the visual representation of the bulb"""
        self.canvas.clear()
        with self.canvas:
            if self.is_broken:
                Color(0.5, 0.5, 0.5, 1)  # Gray for broken
            elif self.is_on:
                # Bright color when on
                intensity = min(1.0, self.brightness / 100.0)
                Color(1, 1, intensity, 1)
            else:
                # Dim color when off
                intensity = max(0.2, self.brightness / 100.0)
                Color(intensity, intensity, intensity, 1)
            
            # Draw bulb
            if self.texture_off and self.texture_on:
                texture = self.texture_on if self.is_on else self.texture_off
                Rectangle(pos=self.pos, size=self.size, texture=texture)
            else:
                # Fallback to simple circle
                Ellipse(pos=self.pos, size=self.size)
    
    def toggle(self):
        """Toggle the bulb on/off"""
        if not self.is_broken:
            self.is_on = not self.is_on
            self.update_graphics()
            return BULB_ON_VALUE if self.is_on else BULB_OFF_VALUE
        return 0
    
    def break_bulb(self):
        """Break the bulb (can't be turned on anymore)"""
        self.is_broken = True
        self.is_on = False
        self.update_graphics()
    
    def decay_brightness(self, dt):
        """Reduce brightness over time"""
        if self.is_on and not self.is_broken:
            self.brightness = max(0, self.brightness - BRIGHTNESS_DECAY * dt * 60)
            if self.brightness <= 0:
                self.break_bulb()
            self.update_graphics()

class Star(Widget):
    """Collectible star that gives bonus points"""
    
    def __init__(self, x, y, **kwargs):
        super().__init__(**kwargs)
        self.pos = (x, y)
        self.size = (30, 30)
        self.collected = False
        
        # Load star texture with proper Android path
        try:
            star_path = get_asset_path('star.png')
            self.texture_star = CoreImage(star_path).texture
        except Exception as e:
            print(f"Error loading star texture: {e}")
            self.texture_star = None
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.update_graphics()
    
    def update_graphics(self, *args):
        """Update the visual representation of the star"""
        if not self.collected:
            self.canvas.clear()
            with self.canvas:
                Color(1, 1, 0, 1)  # Yellow
                if self.texture_star:
                    Rectangle(pos=self.pos, size=self.size, texture=self.texture_star)
                else:
                    # Fallback to simple star shape
                    Ellipse(pos=self.pos, size=self.size)
    
    def collect(self):
        """Collect the star"""
        if not self.collected:
            self.collected = True
            self.canvas.clear()
            return STAR_BONUS
        return 0

class Gremlin(Widget):
    """Enemy that moves around and damages bulbs"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size = (40, 40)
        self.speed = random.uniform(50, 100)
        self.direction = Vector(random.uniform(-1, 1), random.uniform(-1, 1)).normalize()
        
        # Random starting position
        from kivy.core.window import Window
        self.pos = (
            random.randint(0, int(Window.width - self.size[0])),
            random.randint(0, int(Window.height - self.size[1]))
        )
        
        # Load gremlin texture with proper Android path
        try:
            gremlin_path = get_asset_path('gremlin.png')
            self.texture_gremlin = CoreImage(gremlin_path).texture
        except Exception as e:
            print(f"Error loading gremlin texture: {e}")
            self.texture_gremlin = None
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.update_graphics()
    
    def update_graphics(self, *args):
        """Update the visual representation of the gremlin"""
        self.canvas.clear()
        with self.canvas:
            Color(0.5, 0, 0.5, 1)  # Purple
            if self.texture_gremlin:
                Rectangle(pos=self.pos, size=self.size, texture=self.texture_gremlin)
            else:
                # Fallback to simple rectangle
                Rectangle(pos=self.pos, size=self.size)
    
    def update(self, dt):
        """Update gremlin position"""
        from kivy.core.window import Window
        
        # Move gremlin
        new_x = self.x + self.direction.x * self.speed * dt
        new_y = self.y + self.direction.y * self.speed * dt
        
        # Bounce off walls
        if new_x <= 0 or new_x >= Window.width - self.size[0]:
            self.direction.x *= -1
        if new_y <= 0 or new_y >= Window.height - self.size[1]:
            self.direction.y *= -1
        
        # Update position
        self.x = max(0, min(Window.width - self.size[0], new_x))
        self.y = max(0, min(Window.height - self.size[1], new_y))
        
        self.update_graphics()

class Boss(Widget):
    """Boss enemy that appears at higher levels"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size = (80, 80)
        self.health = 3
        self.speed = 30
        self.direction = Vector(1, 0)
        
        # Center position
        from kivy.core.window import Window
        self.pos = (Window.width // 2 - self.size[0] // 2, Window.height // 2 - self.size[1] // 2)
        
        self.bind(pos=self.update_graphics, size=self.update_graphics)
        self.update_graphics()
    
    def update_graphics(self, *args):
        """Update the visual representation of the boss"""
        self.canvas.clear()
        with self.canvas:
            # Color based on health
            if self.health > 2:
                Color(1, 0, 0, 1)  # Red
            elif self.health > 1:
                Color(1, 0.5, 0, 1)  # Orange
            else:
                Color(0.5, 0, 0, 1)  # Dark red
            
            Rectangle(pos=self.pos, size=self.size)
    
    def update(self, dt):
        """Update boss position and behavior"""
        from kivy.core.window import Window
        
        # Move in a pattern
        self.x += self.direction.x * self.speed * dt
        
        # Bounce horizontally
        if self.x <= 0 or self.x >= Window.width - self.size[0]:
            self.direction.x *= -1
        
        self.update_graphics()
    
    def take_damage(self):
        """Boss takes damage"""
        self.health -= 1
        self.update_graphics()
        return self.health <= 0

class GameWidget(FloatLayout):
    """Main game widget that handles all game logic"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Game state
        self.brightness = 100
        self.score = 0
        self.level = 1
        self.lives = 3
        self.game_over = False
        self.paused = False

        # Game objects
        self.bulbs = []
        self.stars = []
        self.gremlins = []
        self.boss = None

        # Sounds
        self.sounds = {}

        # High scores storage (Android-compatible)
        try:
            self.high_scores_store = JsonStore('high_scores.json')
        except:
            self.high_scores_store = None

        # UI
        self.ui = GameUI()
        self.add_widget(self.ui)

        # Load assets
        self.load_assets()

        # Initialize game
        self.setup_level()

        # Start game loop
        Clock.schedule_interval(self.update, 1.0 / FPS)

    def load_assets(self):
        """Load game assets with proper Android file handling"""
        try:
            sound_files = {
                'bulb_click': 'bulb_click.wav',
                'bulb_break': 'bulb_break.wav',
                'gremlin_hit': 'gremlin_hit.wav',
                'star_collect': 'star_collect.wav',
                'boss_attack': 'boss_attack.wav',
                'boss_hit': 'boss_hit.wav'
            }

            for sound_name, filename in sound_files.items():
                try:
                    sound_path = get_asset_path(filename)
                    sound = SoundLoader.load(sound_path)
                    if sound:
                        self.sounds[sound_name] = sound
                    else:
                        print(f"Could not load sound: {filename}")
                except Exception as e:
                    print(f"Error loading sound {filename}: {e}")

        except Exception as e:
            print(f"Error loading assets: {e}")

    def play_sound(self, sound_name):
        """Play a sound if it exists"""
        if sound_name in self.sounds and self.sounds[sound_name]:
            try:
                self.sounds[sound_name].play()
            except Exception as e:
                print(f"Error playing sound {sound_name}: {e}")

    def setup_level(self):
        """Set up a new level"""
        # Clear existing objects
        for bulb in self.bulbs:
            self.remove_widget(bulb)
        for star in self.stars:
            self.remove_widget(star)
        for gremlin in self.gremlins:
            self.remove_widget(gremlin)
        if self.boss:
            self.remove_widget(self.boss)

        self.bulbs.clear()
        self.stars.clear()
        self.gremlins.clear()
        self.boss = None

        # Create bulbs
        from kivy.core.window import Window
        num_bulbs = min(8 + self.level, 15)
        for i in range(num_bulbs):
            x = random.randint(50, int(Window.width - 110))
            y = random.randint(100, int(Window.height - 200))
            bulb = Bulb(x, y)
            self.bulbs.append(bulb)
            self.add_widget(bulb)

        # Create stars
        num_stars = min(2 + self.level // 2, 5)
        for i in range(num_stars):
            x = random.randint(30, int(Window.width - 60))
            y = random.randint(80, int(Window.height - 180))
            star = Star(x, y)
            self.stars.append(star)
            self.add_widget(star)

        # Create gremlins
        num_gremlins = min(1 + self.level // 2, 4)
        for i in range(num_gremlins):
            gremlin = Gremlin()
            self.gremlins.append(gremlin)
            self.add_widget(gremlin)

        # Create boss (every 5 levels)
        if self.level % 5 == 0:
            self.boss = Boss()
            self.add_widget(self.boss)

    def load_high_scores(self):
        """Load high scores from storage"""
        try:
            if self.high_scores_store and self.high_scores_store.exists('scores'):
                return self.high_scores_store.get('scores')['data']
            else:
                # Default high scores
                return [1000, 800, 600, 400, 200]
        except Exception as e:
            print(f"Error loading high scores: {e}")
            return [1000, 800, 600, 400, 200]

    def save_high_scores(self, scores):
        """Save high scores to storage"""
        try:
            if self.high_scores_store:
                self.high_scores_store.put('scores', data=scores)
        except Exception as e:
            print(f"Error saving high scores: {e}")

    def on_touch_down(self, touch):
        """Handle touch events"""
        if self.game_over or self.paused:
            return super().on_touch_down(touch)

        # Check bulb clicks
        for bulb in self.bulbs:
            if bulb.collide_point(*touch.pos):
                brightness_change = bulb.toggle()
                self.brightness += brightness_change
                self.score += abs(brightness_change)
                self.play_sound('bulb_click')
                break

        # Check star collection
        for star in self.stars:
            if star.collide_point(*touch.pos) and not star.collected:
                bonus = star.collect()
                self.brightness += bonus
                self.score += bonus * 2
                self.play_sound('star_collect')
                break

        # Check boss hits
        if self.boss and self.boss.collide_point(*touch.pos):
            if self.boss.take_damage():
                self.remove_widget(self.boss)
                self.boss = None
                self.score += 100
                self.play_sound('boss_hit')
                # Level complete
                self.level += 1
                self.setup_level()
            else:
                self.play_sound('boss_attack')

        return super().on_touch_down(touch)

    def update(self, dt):
        """Main game update loop"""
        if self.game_over or self.paused:
            return

        # Update bulbs
        for bulb in self.bulbs:
            bulb.decay_brightness(dt)

        # Update gremlins
        for gremlin in self.gremlins:
            gremlin.update(dt)

            # Check gremlin-bulb collisions
            for bulb in self.bulbs:
                if gremlin.collide_widget(bulb) and not bulb.is_broken:
                    bulb.break_bulb()
                    self.brightness += GREMLIN_DAMAGE
                    self.play_sound('bulb_break')
                    break

        # Update boss
        if self.boss:
            self.boss.update(dt)

        # Check brightness
        if self.brightness <= 0:
            self.lives -= 1
            if self.lives <= 0:
                self.game_over = True
                self.show_game_over()
            else:
                self.brightness = 50  # Give some brightness back

        # Check level completion (all bulbs broken or very low brightness)
        active_bulbs = [b for b in self.bulbs if not b.is_broken]
        if len(active_bulbs) == 0 and not self.boss:
            self.level += 1
            self.setup_level()

        # Update UI
        self.ui.update_display(self.brightness, self.score, self.level, self.lives)

    def show_game_over(self):
        """Show game over popup"""
        high_scores = self.load_high_scores()

        # Check if this is a high score
        if self.score > min(high_scores):
            high_scores.append(self.score)
            high_scores.sort(reverse=True)
            high_scores = high_scores[:5]  # Keep top 5
            self.save_high_scores(high_scores)

        popup = GameOverPopup(self.score, high_scores, self.restart_game)
        popup.open()

    def restart_game(self):
        """Restart the game"""
        self.brightness = 100
        self.score = 0
        self.level = 1
        self.lives = 3
        self.game_over = False
        self.setup_level()

class TurnMeOnApp(App):
    """Main application class"""

    def build(self):
        """Build the app"""
        # Set window size for testing
        from kivy.core.window import Window
        Window.size = (SCREEN_WIDTH, SCREEN_HEIGHT)

        # Create main game widget
        game = GameWidget()
        return game

if __name__ == '__main__':
    TurnMeOnApp().run()
