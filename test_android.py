"""
Simple test app to verify Android compatibility
"""

from kivy.app import App
from kivy.uix.label import Label
from kivy.uix.button import Button
from kivy.uix.boxlayout import BoxLayout
from kivy.core.audio import SoundLoader
from kivy.core.image import Image as CoreImage
from kivy.resources import resource_find
from kivy.storage.jsonstore import JsonStore
import os

def get_asset_path(filename):
    """Get the correct path for assets on Android"""
    try:
        # Try to find the resource in the app bundle
        path = resource_find(filename)
        if path:
            return path
        # Fallback to assets directory
        return os.path.join('assets', filename)
    except:
        # Final fallback
        return filename

class TestApp(App):
    def build(self):
        layout = BoxLayout(orientation='vertical', padding=20, spacing=10)
        
        # Test label
        label = Label(text='Android Compatibility Test', size_hint=(1, 0.2))
        layout.add_widget(label)
        
        # Test asset loading
        test_button = Button(text='Test Asset Loading', size_hint=(1, 0.2))
        test_button.bind(on_press=self.test_assets)
        layout.add_widget(test_button)
        
        # Test storage
        storage_button = Button(text='Test Storage', size_hint=(1, 0.2))
        storage_button.bind(on_press=self.test_storage)
        layout.add_widget(storage_button)
        
        # Results label
        self.results_label = Label(text='Ready to test...', size_hint=(1, 0.4))
        layout.add_widget(self.results_label)
        
        return layout
    
    def test_assets(self, instance):
        results = []
        
        # Test image loading
        try:
            bulb_path = get_asset_path('bulb_off.png')
            image = CoreImage(bulb_path)
            results.append("✓ Image loading: SUCCESS")
        except Exception as e:
            results.append(f"✗ Image loading: FAILED - {e}")
        
        # Test sound loading
        try:
            sound_path = get_asset_path('bulb_click.wav')
            sound = SoundLoader.load(sound_path)
            if sound:
                results.append("✓ Sound loading: SUCCESS")
            else:
                results.append("✗ Sound loading: FAILED - No sound object")
        except Exception as e:
            results.append(f"✗ Sound loading: FAILED - {e}")
        
        self.results_label.text = '\n'.join(results)
    
    def test_storage(self, instance):
        try:
            # Test JsonStore
            store = JsonStore('test_data.json')
            store.put('test', value='Hello Android!')
            
            if store.exists('test'):
                data = store.get('test')
                self.results_label.text = f"✓ Storage: SUCCESS\nStored: {data['value']}"
            else:
                self.results_label.text = "✗ Storage: FAILED - Data not found"
                
        except Exception as e:
            self.results_label.text = f"✗ Storage: FAILED - {e}"

if __name__ == '__main__':
    TestApp().run()
