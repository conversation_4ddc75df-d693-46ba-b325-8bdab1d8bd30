# 🎮 Turn Me On Game - Android Studio Workflow

## Quick Start Commands

```bash
# 1. Check emulator status
./android_studio_setup.sh check

# 2. Start emulator (if not running)
./android_studio_setup.sh start

# 3. Build and install your game
./android_studio_setup.sh build

# 4. View app logs
./android_studio_setup.sh logs

# 5. Open Android Studio
./android_studio_setup.sh studio
```

## Development Workflow

### 1. **Code Development** (Python/Kivy)
- Edit your game in `main.py`
- Test locally: `python main.py`
- Cross-platform code works on both iOS and Android

### 2. **Android Testing**
```bash
# Build APK
source venv/bin/activate
buildozer android debug

# Install on emulator
adb install -r bin/turnmeongame-*.apk
```

### 3. **Android Studio Features**

#### **Device Management**
- **Tools → AVD Manager**: Manage virtual devices
- **Tools → Device Manager**: See connected devices
- **View → Tool Windows → Logcat**: Monitor app logs

#### **Debugging**
```bash
# View filtered logs for your app
adb logcat | grep -i "python\|kivy\|turnmeongame"

# Install APK with debugging
adb install -r -d bin/turnmeongame-*.apk

# Clear app data
adb shell pm clear com.amazingsauce.turnmeongame
```

#### **Performance Monitoring**
- **View → Tool Windows → Profiler**: Monitor app performance
- **Tools → Layout Inspector**: Inspect UI (limited for Kivy apps)

### 4. **File Structure**
```
turnMeOnGameAndroid/
├── main.py                 # Your cross-platform game code
├── buildozer.spec         # Android build configuration
├── android_studio_setup.sh # Helper script
├── venv/                  # Python virtual environment
├── bin/                   # Built APK files
└── .buildozer/           # Build cache
```

### 5. **Common Tasks**

#### **Update App Icon**
1. Create icon files in `res/` directory
2. Update `buildozer.spec`: `icon.filename = res/icon.png`
3. Rebuild: `buildozer android debug`

#### **Add Permissions**
Edit `buildozer.spec`:
```ini
android.permissions = INTERNET, WRITE_EXTERNAL_STORAGE
```

#### **Change App Name**
Edit `buildozer.spec`:
```ini
title = Turn Me On Game
package.name = turnmeongame
package.domain = com.amazingsauce
```

### 6. **Troubleshooting**

#### **Build Issues**
```bash
# Clean build
buildozer android clean

# Verbose build
buildozer android debug --verbose

# Check buildozer logs
cat .buildozer/logs/buildozer.log
```

#### **Emulator Issues**
```bash
# Restart emulator
adb reboot

# Kill and restart ADB
adb kill-server && adb start-server

# List all devices
adb devices -l
```

#### **App Crashes**
```bash
# View crash logs
adb logcat | grep -E "(FATAL|AndroidRuntime)"

# Clear app cache
adb shell pm clear com.amazingsauce.turnmeongame
```

## Benefits of This Setup

✅ **Cross-Platform**: One codebase for iOS and Android
✅ **Professional Tools**: Full Android Studio debugging capabilities  
✅ **Easy Testing**: Quick APK builds and emulator deployment
✅ **Performance Monitoring**: Android Studio profiling tools
✅ **Device Testing**: Test on multiple Android versions/devices
✅ **Automated Workflow**: Helper scripts for common tasks

## Next Steps

1. **Complete the build** (currently in progress)
2. **Test your game** on the Android emulator
3. **Iterate and improve** using Android Studio's debugging tools
4. **Deploy to real devices** for testing
5. **Publish to Google Play Store** when ready
