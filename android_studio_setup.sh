#!/bin/bash

# Android Studio Integration Script for Turn Me On Game
# This script helps you test your Kivy app with Android Studio emulator

# Set Android and Java environment variables
export ANDROID_HOME=~/Library/Android/sdk
export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
export PATH="$JAVA_HOME/bin:$PATH:$ANDROID_HOME/emulator:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools"

echo "🎮 Turn Me On Game - Android Studio Integration"
echo "=============================================="

# Function to check emulator status
check_emulator() {
    echo "📱 Checking Android emulator status..."
    adb devices
}

# Function to start emulator
start_emulator() {
    echo "🚀 Starting Android emulator..."
    $ANDROID_HOME/emulator/emulator -avd Medium_Phone_API_36 -no-snapshot-load &
    echo "⏳ Waiting for emulator to boot..."
    adb wait-for-device
    echo "✅ Emulator is ready!"
}

# Function to build and install APK
build_and_install() {
    echo "🔨 Building APK with buildozer..."
    source venv/bin/activate
    buildozer android debug
    
    if [ -f "bin/turnmeongame-0.1-arm64-v8a-debug.apk" ]; then
        echo "📦 Installing APK on emulator..."
        adb install -r bin/turnmeongame-0.1-arm64-v8a-debug.apk
        echo "✅ APK installed successfully!"
        echo "🎮 You can now find 'Turn Me On Game' in the emulator's app drawer"
    else
        echo "❌ APK build failed or not found"
    fi
}

# Function to view logs
view_logs() {
    echo "📋 Viewing Android logs (Ctrl+C to exit)..."
    adb logcat | grep -i "python\|kivy\|turnmeongame"
}

# Function to open Android Studio
open_android_studio() {
    echo "🛠️ Opening Android Studio..."
    open "/Applications/Android Studio.app"
}

# Main menu
case "$1" in
    "check")
        check_emulator
        ;;
    "start")
        start_emulator
        ;;
    "build")
        build_and_install
        ;;
    "logs")
        view_logs
        ;;
    "studio")
        open_android_studio
        ;;
    *)
        echo "Usage: $0 {check|start|build|logs|studio}"
        echo ""
        echo "Commands:"
        echo "  check  - Check emulator status"
        echo "  start  - Start Android emulator"
        echo "  build  - Build and install APK"
        echo "  logs   - View app logs"
        echo "  studio - Open Android Studio"
        echo ""
        echo "Example workflow:"
        echo "  1. ./android_studio_setup.sh start"
        echo "  2. ./android_studio_setup.sh build"
        echo "  3. ./android_studio_setup.sh logs"
        ;;
esac
