package com.amazingsauce.turnmeongame

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SportsEsports
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import com.amazingsauce.turnmeongame.ui.theme.TurnMeOnGameTheme

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Enable edge-to-edge display
        enableEdgeToEdge()
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            TurnMeOnGameTheme {
                GameScreen()
            }
        }
    }
}

@Composable
fun GameScreen() {
    var gameStarted by remember { mutableStateOf(false) }

    if (gameStarted) {
        TurnMeOnGame(onBackToMenu = { gameStarted = false })
    } else {
        MainMenu(onStartGame = { gameStarted = true })
    }
}

@Composable
fun MainMenu(onStartGame: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.systemBars)
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF2196F3), // Blue
                        Color(0xFF9C27B0)  // Purple
                    )
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(30.dp),
            modifier = Modifier.padding(24.dp)
        ) {
            // Game Controller Icon using custom drawable
            Icon(
                painter = painterResource(id = R.drawable.ic_game_controller),
                contentDescription = "Game Controller",
                modifier = Modifier.size(80.dp),
                tint = Color.White
            )

            // Game Title
            Text(
                text = "Turn Me On Game",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                textAlign = TextAlign.Center
            )

            // Welcome Text
            Text(
                text = "Tap the bulbs to turn them on!\nKeep the lights bright!",
                fontSize = 18.sp,
                color = Color.White.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            )

            // Start Game Button
            Button(
                onClick = onStartGame,
                modifier = Modifier
                    .padding(horizontal = 40.dp)
                    .height(50.dp),
                shape = RoundedCornerShape(25.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color.White,
                    contentColor = Color(0xFF2196F3)
                )
            ) {
                Text(
                    text = "Start Game",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

@Composable
fun TurnMeOnGame(onBackToMenu: () -> Unit) {
    var bulbStates by remember { mutableStateOf(List(16) { false }) }
    var score by remember { mutableStateOf(0) }
    var brightness by remember { mutableStateOf(50f) }

    // Brightness decay effect
    LaunchedEffect(Unit) {
        while (true) {
            kotlinx.coroutines.delay(1000) // Every second
            brightness = (brightness - 2f).coerceAtLeast(0f)
            if (brightness <= 0f) {
                // Game over - reset
                bulbStates = List(16) { false }
                score = 0
                brightness = 50f
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.systemBars)
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        Color(0xFF1A1A2E), // Dark blue
                        Color(0xFF16213E)  // Darker blue
                    )
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header with score and brightness
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Button(
                    onClick = onBackToMenu,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White.copy(alpha = 0.2f)
                    )
                ) {
                    Text("Menu", color = Color.White)
                }

                Column(horizontalAlignment = Alignment.End) {
                    Text(
                        text = "Score: $score",
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    Text(
                        text = "Brightness: ${brightness.toInt()}%",
                        color = when {
                            brightness > 70 -> Color.Green
                            brightness > 30 -> Color.Yellow
                            else -> Color.Red
                        },
                        fontSize = 16.sp
                    )
                }
            }

            // Brightness bar
            LinearProgressIndicator(
                progress = brightness / 100f,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp),
                color = when {
                    brightness > 70 -> Color.Green
                    brightness > 30 -> Color.Yellow
                    else -> Color.Red
                },
                trackColor = Color.White.copy(alpha = 0.3f)
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Bulb grid
            LazyVerticalGrid(
                columns = GridCells.Fixed(4),
                contentPadding = PaddingValues(8.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier.fillMaxSize()
            ) {
                items(bulbStates.indices.toList()) { index ->
                    BulbItem(
                        isOn = bulbStates[index],
                        onClick = {
                            val newStates = bulbStates.toMutableList()
                            newStates[index] = !newStates[index]
                            bulbStates = newStates

                            if (newStates[index]) {
                                // Bulb turned on
                                score += 10
                                brightness = (brightness + 15f).coerceAtMost(100f)
                            } else {
                                // Bulb turned off
                                score = (score - 5).coerceAtLeast(0)
                                brightness = (brightness - 5f).coerceAtLeast(0f)
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun BulbItem(isOn: Boolean, onClick: () -> Unit) {
    Card(
        modifier = Modifier
            .aspectRatio(1f)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isOn) Color.Yellow.copy(alpha = 0.3f) else Color.Gray.copy(alpha = 0.3f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                painter = painterResource(
                    id = if (isOn) R.drawable.ic_bulb_on else R.drawable.ic_bulb_off
                ),
                contentDescription = if (isOn) "Bulb On" else "Bulb Off",
                modifier = Modifier.size(48.dp),
                tint = if (isOn) Color.Yellow else Color.Gray
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun GameScreenPreview() {
    TurnMeOnGameTheme {
        GameScreen()
    }
}
